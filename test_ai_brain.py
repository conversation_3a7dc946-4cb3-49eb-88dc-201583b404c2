#!/usr/bin/env python3
"""
Тестирование ИИ модуля Jarvis
"""

import asyncio
from jarvis.modules.ai_brain import AIBrain
from jarvis.core.logger import jarvis_logger

async def test_ai_brain():
    """Тестирование ИИ модуля"""
    logger = jarvis_logger
    logger.info("🧪 Starting AI Brain Test")
    
    # Инициализация
    ai_brain = AIBrain()
    success = await ai_brain.initialize()
    
    if not success:
        logger.error("❌ Failed to initialize AI Brain")
        return
    
    logger.info("✅ AI Brain initialized successfully")
    
    # Тестовые сообщения
    test_messages = [
        "Привет!",
        "Как дела?",
        "Расскажи о себе",
        "Что ты умеешь?",
        "Какой сейчас год?",
        "Как тебя зовут?",
        "Спасибо за помощь",
        "Что нового?",
        "Помоги мне",
        "До свидания"
    ]
    
    logger.info("🗣️ Testing AI responses:")
    
    for i, message in enumerate(test_messages, 1):
        logger.info(f"\n--- Test {i}/10 ---")
        logger.info(f"👤 User: {message}")
        
        try:
            response = await ai_brain.process_message(message)
            logger.info(f"🤖 AI: {response}")
        except Exception as e:
            logger.error(f"❌ Error processing message: {e}")
        
        # Небольшая пауза между сообщениями
        await asyncio.sleep(0.5)
    
    # Получаем сводку разговора
    summary = ai_brain.get_conversation_summary()
    logger.info(f"\n📊 Conversation Summary:")
    logger.info(f"   Total interactions: {summary.get('total_interactions', 0)}")
    logger.info(f"   Recent topics: {summary.get('recent_topics', [])}")
    logger.info(f"   Dominant emotion: {summary.get('dominant_emotion', 'neutral')}")
    
    # Очистка
    await ai_brain.cleanup()
    logger.info("\n🎉 AI Brain test completed!")

if __name__ == "__main__":
    asyncio.run(test_ai_brain())
