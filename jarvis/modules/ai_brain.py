"""
Модуль искусственного интеллекта для естественного общения Jarvis
"""

import asyncio
import json
import re
import random
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from jarvis.core.base import BaseModule, CommandResult
from jarvis.core.logger import jarvis_logger
from jarvis.config import config

class AIBrain(BaseModule):
    """Модуль ИИ для естественного общения и понимания контекста"""
    
    def __init__(self):
        super().__init__("AIBrain")

        # Память и контекст
        self.conversation_history = []
        self.user_context = {}
        self.current_mood = "neutral"
        self.last_interaction_time = 0

        # База знаний
        self.knowledge_base = {}
        self.personality_traits = {}
        self.response_templates = {}
        self.humor_database = {}
        self.wisdom_database = {}
        self.reasoning_database = {}

        # Пути к файлам данных
        self.data_dir = config.project_root / "data" / "ai_brain"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
    async def initialize(self) -> bool:
        """Инициализация ИИ модуля"""
        try:
            self.logger.info("Initializing AI Brain Module...")

            # Загружаем базу знаний
            await self._load_knowledge_base()

            # Загружаем личность
            await self._load_personality()

            # Загружаем шаблоны ответов
            await self._load_response_templates()

            # Загружаем базу юмора
            await self._load_humor_database()

            # Загружаем базу мудрости
            await self._load_wisdom_database()

            # Загружаем базу рассуждений
            await self._load_reasoning_database()

            # Загружаем контекст пользователя
            await self._load_user_context()

            self.logger.info("🧠 AI Brain Module initialized successfully")
            self.is_initialized = True
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize AI Brain Module: {e}")
            return False
    
    async def _load_knowledge_base(self):
        """Загрузка базы знаний"""
        knowledge_file = self.data_dir / "knowledge_base.json"
        
        default_knowledge = {
            "about_jarvis": {
                "name": "Джарвис",
                "role": "персональный ИИ-помощник",
                "creator": "пользователь",
                "capabilities": [
                    "голосовое управление",
                    "открытие приложений",
                    "естественное общение",
                    "помощь в задачах"
                ]
            },
            "general_facts": {
                "current_year": 2025,
                "system": "macOS",
                "language": "русский"
            },
            "common_topics": {
                "greeting": ["привет", "здравствуй", "добро пожаловать"],
                "farewell": ["пока", "до свидания", "увидимся"],
                "gratitude": ["спасибо", "благодарю", "признателен"],
                "weather": ["погода", "температура", "дождь", "солнце"],
                "time": ["время", "час", "минута", "сейчас"]
            }
        }
        
        if knowledge_file.exists():
            with open(knowledge_file, 'r', encoding='utf-8') as f:
                self.knowledge_base = json.load(f)
        else:
            self.knowledge_base = default_knowledge
            await self._save_knowledge_base()
    
    async def _load_personality(self):
        """Загрузка личностных характеристик"""
        personality_file = self.data_dir / "personality.json"
        
        default_personality = {
            "traits": {
                "helpful": 0.9,
                "friendly": 0.8,
                "professional": 0.7,
                "humorous": 0.6,
                "curious": 0.7,
                "patient": 0.8
            },
            "communication_style": {
                "formality": "умеренная",
                "verbosity": "средняя",
                "emoji_usage": "умеренное",
                "humor_type": "легкий"
            },
            "preferences": {
                "response_length": "medium",
                "explanation_detail": "balanced",
                "proactive_suggestions": True
            }
        }
        
        if personality_file.exists():
            with open(personality_file, 'r', encoding='utf-8') as f:
                self.personality_traits = json.load(f)
        else:
            self.personality_traits = default_personality
            await self._save_personality()
    
    async def _load_response_templates(self):
        """Загрузка шаблонов ответов"""
        templates_file = self.data_dir / "response_templates.json"
        
        default_templates = {
            "greeting": [
                "Привет! Как дела?",
                "Здравствуйте! Чем могу помочь?",
                "Добро пожаловать! Готов к работе!",
                "Привет! Рад вас видеть!"
            ],
            "farewell": [
                "До свидания! Хорошего дня!",
                "Пока! Обращайтесь, если что-то понадобится!",
                "До встречи! Удачи!",
                "Всего доброго!"
            ],
            "gratitude_response": [
                "Пожалуйста! Всегда рад помочь!",
                "Не за что! Это моя работа!",
                "Обращайтесь! Я здесь для этого!",
                "Рад был помочь!"
            ],
            "unknown": [
                "Интересно... Расскажите подробнее!",
                "Не совсем понял, но готов разобраться!",
                "Хм, это что-то новое для меня. Объясните?",
                "Любопытно! Давайте обсудим это!"
            ],
            "thinking": [
                "Дайте подумать...",
                "Интересный вопрос...",
                "Размышляю над этим...",
                "Хороший вопрос, анализирую..."
            ],
            "encouragement": [
                "Отлично!",
                "Замечательно!",
                "Прекрасно!",
                "Великолепно!"
            ]
        }
        
        if templates_file.exists():
            with open(templates_file, 'r', encoding='utf-8') as f:
                self.response_templates = json.load(f)
        else:
            self.response_templates = default_templates
            await self._save_response_templates()
    
    async def _load_humor_database(self):
        """Загрузка базы юмора"""
        humor_file = self.data_dir / "humor_database.json"

        if humor_file.exists():
            with open(humor_file, 'r', encoding='utf-8') as f:
                self.humor_database = json.load(f)
        else:
            self.humor_database = {"jokes": {}, "puns": [], "funny_facts": []}

    async def _load_wisdom_database(self):
        """Загрузка базы мудрости"""
        wisdom_file = self.data_dir / "wisdom_database.json"

        if wisdom_file.exists():
            with open(wisdom_file, 'r', encoding='utf-8') as f:
                self.wisdom_database = json.load(f)
        else:
            self.wisdom_database = {"philosophical_thoughts": [], "life_advice": [], "deep_questions": []}

    async def _load_reasoning_database(self):
        """Загрузка базы рассуждений"""
        reasoning_file = self.data_dir / "reasoning_database.json"

        if reasoning_file.exists():
            with open(reasoning_file, 'r', encoding='utf-8') as f:
                self.reasoning_database = json.load(f)
        else:
            self.reasoning_database = {"reasoning_patterns": {}, "logical_frameworks": {}}

    async def _load_user_context(self):
        """Загрузка контекста пользователя"""
        context_file = self.data_dir / "user_context.json"

        if context_file.exists():
            with open(context_file, 'r', encoding='utf-8') as f:
                self.user_context = json.load(f)
        else:
            self.user_context = {
                "name": None,
                "preferences": {},
                "interaction_count": 0,
                "last_seen": None,
                "topics_discussed": [],
                "mood_history": []
            }
    
    async def process_message(self, message: str, context: Dict = None) -> str:
        """Основной метод обработки сообщений"""
        try:
            self.logger.info(f"🧠 Processing message: '{message}'")
            
            # Обновляем контекст
            await self._update_interaction_context(message)
            
            # Анализируем сообщение
            analysis = await self._analyze_message(message)
            
            # Генерируем ответ
            response = await self._generate_response(message, analysis, context)
            
            # Сохраняем в историю
            await self._save_to_history(message, response, analysis)
            
            self.logger.info(f"🗣️ Generated response: '{response}'")
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            return "Извините, произошла ошибка в обработке вашего сообщения."
    
    async def _analyze_message(self, message: str) -> Dict:
        """Анализ сообщения для понимания намерений и эмоций"""
        message_lower = message.lower()
        
        analysis = {
            "intent": "unknown",
            "emotion": "neutral",
            "topics": [],
            "entities": [],
            "question_type": None,
            "urgency": "normal"
        }
        
        # Определяем намерение
        if any(word in message_lower for word in ["привет", "здравствуй", "добрый"]):
            analysis["intent"] = "greeting"
        elif any(word in message_lower for word in ["пока", "до свидания", "увидимся"]):
            analysis["intent"] = "farewell"
        elif any(word in message_lower for word in ["спасибо", "благодарю"]):
            analysis["intent"] = "gratitude"
        elif any(word in message_lower for word in ["как дела", "как жизнь", "что нового"]):
            analysis["intent"] = "small_talk"
        elif any(word in message_lower for word in ["расскажи что-нибудь", "интересное", "расскажи факт", "удиви"]):
            analysis["intent"] = "request_interesting"
        elif any(word in message_lower for word in ["шутка", "анекдот", "смешное", "рассмеши"]):
            analysis["intent"] = "request_humor"
        elif any(word in message_lower for word in ["совет", "что делать", "помоги решить", "как поступить"]):
            analysis["intent"] = "request_advice"
        elif any(word in message_lower for word in ["скучно", "нечем заняться", "развлеки"]):
            analysis["intent"] = "boredom"
        elif any(word in message_lower for word in ["думаешь", "мнение", "считаешь"]):
            analysis["intent"] = "opinion_request"
        elif any(word in message_lower for word in ["проанализируй", "разбери", "рассуди", "логично"]):
            analysis["intent"] = "reasoning_request"
        elif any(word in message_lower for word in ["сравни", "выбери", "что лучше", "за и против"]):
            analysis["intent"] = "comparison_request"
        elif "?" in message or any(word in message_lower for word in ["что", "как", "где", "когда", "почему"]):
            analysis["intent"] = "question"
            analysis["question_type"] = self._detect_question_type(message_lower)
        elif any(word in message_lower for word in ["открой", "запусти", "закрой"]):
            analysis["intent"] = "command"
        
        # Определяем эмоцию (расширенный анализ)
        if any(word in message_lower for word in ["отлично", "супер", "классно", "здорово", "прекрасно", "замечательно", "восхитительно", "круто"]):
            analysis["emotion"] = "positive"
        elif any(word in message_lower for word in ["плохо", "грустно", "ужасно", "проблема", "печально", "расстроен", "злой", "сердитый"]):
            analysis["emotion"] = "negative"
        elif any(word in message_lower for word in ["устал", "скучно", "надоело", "лень"]):
            analysis["emotion"] = "tired"
        elif any(word in message_lower for word in ["удивительно", "вау", "невероятно", "поразительно"]):
            analysis["emotion"] = "surprised"
        elif any(word in message_lower for word in ["смешно", "ха-ха", "прикольно", "забавно"]):
            analysis["emotion"] = "amused"
        elif any(word in message_lower for word in ["!!", "!!!", "срочно", "быстро"]):
            analysis["urgency"] = "high"
        
        # Извлекаем темы
        for topic, keywords in self.knowledge_base.get("common_topics", {}).items():
            if any(keyword in message_lower for keyword in keywords):
                analysis["topics"].append(topic)
        
        return analysis

    def _detect_question_type(self, message: str) -> str:
        """Определение типа вопроса"""
        if any(word in message for word in ["что", "какой", "какая"]):
            return "what"
        elif any(word in message for word in ["как", "каким образом"]):
            return "how"
        elif any(word in message for word in ["где", "куда"]):
            return "where"
        elif any(word in message for word in ["когда", "во сколько"]):
            return "when"
        elif any(word in message for word in ["почему", "зачем"]):
            return "why"
        elif any(word in message for word in ["кто", "чей"]):
            return "who"
        return "general"

    async def _generate_response(self, message: str, analysis: Dict, context: Dict = None) -> str:
        """Генерация ответа на основе анализа"""
        intent = analysis["intent"]
        emotion = analysis["emotion"]

        # Базовые ответы по намерениям
        if intent == "greeting":
            response = self._get_random_template("greeting")
            # Персонализируем приветствие
            if self.user_context.get("name"):
                response = f"{response.split('!')[0]}, {self.user_context['name']}!"

        elif intent == "farewell":
            response = self._get_random_template("farewell")

        elif intent == "gratitude":
            response = self._get_random_template("gratitude_response")

        elif intent == "small_talk":
            response = await self._handle_small_talk(message, analysis)

        elif intent == "request_interesting":
            response = await self._handle_interesting_request(message, analysis)

        elif intent == "request_humor":
            response = await self._handle_humor_request(message, analysis)

        elif intent == "request_advice":
            response = await self._handle_advice_request(message, analysis)

        elif intent == "boredom":
            response = await self._handle_boredom(message, analysis)

        elif intent == "opinion_request":
            response = await self._handle_opinion_request(message, analysis)

        elif intent == "reasoning_request":
            response = await self._handle_reasoning_request(message, analysis)

        elif intent == "comparison_request":
            response = await self._handle_comparison_request(message, analysis)

        elif intent == "question":
            response = await self._handle_question(message, analysis)

        elif intent == "command":
            response = "Понял! Выполняю команду..."

        else:
            response = await self._handle_unknown(message, analysis)

        # Добавляем эмоциональную окраску
        response = await self._add_emotional_context(response, emotion)

        # Добавляем персональные элементы
        response = await self._personalize_response(response, analysis)

        # Иногда добавляем инициативные элементы
        response = await self._add_proactive_elements(response, analysis)

        return response

    def _get_random_template(self, template_type: str) -> str:
        """Получение случайного шаблона ответа"""
        templates = self.response_templates.get(template_type, ["Понял!"])
        return random.choice(templates)

    async def _handle_small_talk(self, message: str, analysis: Dict) -> str:
        """Обработка светской беседы"""
        responses = [
            "У меня все отлично! Работаю и помогаю вам. А у вас как дела?",
            "Прекрасно! Готов к новым задачам. Что планируете?",
            "Все хорошо! Изучаю новое и совершенствуюсь. А вы как?",
            "Замечательно! Рад нашему общению. Чем займемся?"
        ]

        base_response = random.choice(responses)

        # Добавляем контекст времени
        current_hour = datetime.now().hour
        if 6 <= current_hour < 12:
            time_context = " Доброе утро, кстати!"
        elif 12 <= current_hour < 18:
            time_context = " Хорошего дня!"
        elif 18 <= current_hour < 22:
            time_context = " Приятного вечера!"
        else:
            time_context = " Поздновато работаете!"

        return base_response + time_context

    async def _handle_interesting_request(self, message: str, analysis: Dict) -> str:
        """Обработка запросов на интересные факты"""
        # Определяем тему по ключевым словам
        topics = analysis.get("topics", [])

        # Ищем подходящий факт
        interesting_facts = self.knowledge_base.get("interesting_facts", {})

        if "science" in topics and "science" in interesting_facts:
            fact_data = random.choice(interesting_facts["science"])
            return f"Вот интересный научный факт: {fact_data['fact']}"
        elif "history" in topics and "history" in interesting_facts:
            fact_data = random.choice(interesting_facts["history"])
            return f"Интересный исторический факт: {fact_data['fact']}"
        elif "nature" in topics and "nature" in interesting_facts:
            fact_data = random.choice(interesting_facts["nature"])
            return f"Удивительный факт о природе: {fact_data['fact']}"
        elif "technology" in topics and "technology" in interesting_facts:
            fact_data = random.choice(interesting_facts["technology"])
            return f"Интересно о технологиях: {fact_data['fact']}"
        else:
            # Выбираем случайный факт из любой категории
            all_facts = []
            for category in interesting_facts.values():
                if isinstance(category, list):
                    all_facts.extend(category)

            if all_facts:
                fact_data = random.choice(all_facts)
                return f"Вот интересный факт: {fact_data['fact']}"
            else:
                return "Хм, дайте подумать... А знаете ли вы, что человеческий мозг потребляет около 20% всей энергии тела? Удивительно, правда?"

    async def _handle_humor_request(self, message: str, analysis: Dict) -> str:
        """Обработка запросов на юмор"""
        humor_db = self.humor_database

        # Определяем тип юмора по контексту
        topics = analysis.get("topics", [])

        if "programming" in message.lower() or "technology" in topics:
            jokes = humor_db.get("jokes", {}).get("programming", [])
        elif "science" in topics:
            jokes = humor_db.get("jokes", {}).get("science", [])
        else:
            jokes = humor_db.get("jokes", {}).get("general", [])

        if jokes:
            joke = random.choice(jokes)
            return f"{joke['setup']} {joke['punchline']}"

        # Если нет подходящих шуток, используем каламбур
        puns = humor_db.get("puns", [])
        if puns:
            pun = random.choice(puns)
            return pun["text"]

        # Запасной вариант
        return "Почему программисты не любят природу? Слишком много багов! 😄"

    async def _handle_advice_request(self, message: str, analysis: Dict) -> str:
        """Обработка запросов на советы"""
        wisdom_db = self.wisdom_database
        life_advice = wisdom_db.get("life_advice", [])

        # Пытаемся найти подходящий совет по ключевым словам
        message_lower = message.lower()

        for advice_item in life_advice:
            situation = advice_item.get("situation", "")
            keywords = advice_item.get("keywords", [])

            if any(keyword in message_lower for keyword in keywords):
                return f"Вот мой совет: {advice_item['advice']}"

        # Если не нашли подходящий, даем общий совет
        if life_advice:
            advice_item = random.choice(life_advice)
            return f"Позвольте поделиться мыслью: {advice_item['advice']}"

        return "Мой совет: прислушивайтесь к своему внутреннему голосу, но не забывайте анализировать ситуацию логически. Баланс интуиции и разума - ключ к мудрым решениям."

    async def _handle_boredom(self, message: str, analysis: Dict) -> str:
        """Обработка скуки - предложение активностей"""
        suggestions = [
            "Как насчет изучения чего-то нового? Могу рассказать интересный факт!",
            "Предлагаю поразмышлять над философским вопросом. Интересно?",
            "А что если мы обсудим ваши планы или мечты?",
            "Могу рассказать шутку или интересную историю!",
            "Как насчет того, чтобы поговорить о ваших увлечениях?"
        ]

        base_suggestion = random.choice(suggestions)

        # Добавляем конкретное предложение
        conversation_starters = self.humor_database.get("conversation_starters", [])
        if conversation_starters:
            starter = random.choice(conversation_starters)
            return f"{base_suggestion} {starter['question']}"

        return base_suggestion

    async def _handle_opinion_request(self, message: str, analysis: Dict) -> str:
        """Обработка запросов мнения"""
        wisdom_db = self.wisdom_database
        philosophical_thoughts = wisdom_db.get("philosophical_thoughts", [])

        # Ищем подходящую философскую мысль
        message_lower = message.lower()

        for thought_item in philosophical_thoughts:
            keywords = thought_item.get("keywords", [])
            if any(keyword in message_lower for keyword in keywords):
                return f"Я думаю так: {thought_item['thought']}"

        # Если не нашли подходящую, даем общий ответ
        thinking_phrases = [
            "Интересный вопрос! Мне кажется...",
            "Размышляя над этим, я думаю...",
            "Это заставляет задуматься... По-моему...",
            "Хороший вопрос! Я считаю..."
        ]

        thinking_phrase = random.choice(thinking_phrases)

        if philosophical_thoughts:
            thought_item = random.choice(philosophical_thoughts)
            return f"{thinking_phrase} {thought_item['thought']}"

        return f"{thinking_phrase} каждый вопрос имеет множество граней. Важно рассматривать проблему с разных сторон."

    async def _handle_reasoning_request(self, message: str, analysis: Dict) -> str:
        """Обработка запросов на рассуждения"""
        reasoning_db = self.reasoning_database
        message_lower = message.lower()

        # Определяем тип рассуждения
        reasoning_patterns = reasoning_db.get("reasoning_patterns", {})

        # Ищем подходящий паттерн рассуждения
        if any(word in message_lower for word in ["проблема", "ситуация", "сложность"]):
            pattern_type = "problem_solving"
        elif any(word in message_lower for word in ["философия", "смысл", "понимание"]):
            pattern_type = "philosophical_reasoning"
        else:
            pattern_type = "analytical_thinking"

        patterns = reasoning_patterns.get(pattern_type, [])
        if patterns:
            pattern = random.choice(patterns)
            steps = pattern.get("steps", [])

            # Выбираем несколько шагов для ответа
            selected_steps = random.sample(steps, min(2, len(steps)))
            reasoning_response = " ".join(selected_steps)

            # Добавляем логический коннектор
            connectors = reasoning_db.get("reasoning_connectors", ["Следовательно..."])
            connector = random.choice(connectors)

            return f"{reasoning_response} {connector} важно подходить к этому вопросу системно."

        # Запасной вариант
        return "Давайте разберем это логически. Сначала определим ключевые факторы, затем проанализируем их взаимосвязи."

    async def _handle_comparison_request(self, message: str, analysis: Dict) -> str:
        """Обработка запросов на сравнение"""
        reasoning_db = self.reasoning_database

        # Используем фреймворк принятия решений
        frameworks = reasoning_db.get("decision_making_frameworks", [])
        if frameworks:
            framework = random.choice(frameworks)
            steps = framework.get("steps", [])

            intro = f"Для сравнения предлагаю использовать {framework.get('name', 'системный подход')}."
            reasoning_steps = " ".join(steps[:2])  # Берем первые два шага

            return f"{intro} {reasoning_steps}"

        # Запасной вариант - простое сравнение
        return "Давайте сравним варианты. С одной стороны... С другой стороны... Взвесив все за и против, можно сделать обоснованный выбор."

    async def _handle_question(self, message: str, analysis: Dict) -> str:
        """Обработка вопросов"""
        question_type = analysis.get("question_type", "general")

        # Проверяем, есть ли ответ в базе знаний
        knowledge_response = await self._search_knowledge(message)
        if knowledge_response:
            return knowledge_response

        # Генерируем ответ в зависимости от типа вопроса с элементами рассуждения
        thinking_phrase = self._get_random_template("thinking")

        # Добавляем критические вопросы для более глубокого анализа
        reasoning_db = self.reasoning_database
        critical_questions = reasoning_db.get("critical_thinking_questions", [])

        if question_type == "what":
            if critical_questions and random.random() < 0.4:  # 40% шанс добавить критический вопрос
                critical_q = random.choice(critical_questions)
                return f"{thinking_phrase} Это интересный вопрос! {critical_q} Расскажите, в каком контексте вас это интересует?"
            return f"{thinking_phrase} Это интересный вопрос! Расскажите, в каком контексте вас это интересует?"

        elif question_type == "how":
            # Используем аналитический подход
            return f"{thinking_phrase} Хороший вопрос! Давайте разберем это пошагово. Сначала определим ключевые компоненты, затем проанализируем их взаимодействие."

        elif question_type == "when":
            return f"Что касается времени... Это зависит от многих факторов. Давайте рассмотрим основные переменные."

        elif question_type == "where":
            return f"По поводу местоположения... Нужно учесть контекст и критерии поиска. Что именно вы ищете?"

        elif question_type == "why":
            # Используем причинно-следственный анализ
            connectors = reasoning_db.get("reasoning_connectors", ["Следовательно..."])
            connector = random.choice(connectors)
            return f"{thinking_phrase} Философский вопрос! Давайте проследим причинно-следственные связи. {connector} важно рассмотреть это с разных углов."

        else:
            if critical_questions and random.random() < 0.3:
                critical_q = random.choice(critical_questions)
                return f"{thinking_phrase} {critical_q} Помогите мне понять вопрос лучше."
            return f"{thinking_phrase} Интересно! Помогите мне понять вопрос лучше."

    async def _handle_unknown(self, message: str, analysis: Dict) -> str:
        """Обработка неизвестных сообщений"""
        message_lower = message.lower()

        # Пытаемся найти ключевые слова для контекста
        if any(word in message_lower for word in ["помощь", "помоги", "нужно"]):
            return "Конечно, помогу! Расскажите подробнее, что нужно сделать?"
        elif any(word in message_lower for word in ["расскажи", "объясни"]):
            return "С удовольствием расскажу! О чем именно хотите узнать?"
        elif any(word in message_lower for word in ["можешь", "умеешь"]):
            capabilities = self.knowledge_base.get("about_jarvis", {}).get("capabilities", [])
            return f"Я умею: {', '.join(capabilities)}. А еще могу поддержать интересную беседу! Что попробуем?"
        elif any(word in message_lower for word in ["грустно", "плохо", "печально"]):
            return "Понимаю ваши чувства... Хотите поговорить об этом? Или может, я расскажу что-то воодушевляющее?"
        elif any(word in message_lower for word in ["хорошо", "отлично", "здорово"]):
            return "Замечательно! Рад, что у вас хорошее настроение! Чем займемся?"

        # Если ничего не подошло, пытаемся быть любопытными
        curiosity_responses = [
            "Интересно... Расскажите подробнее! Мне любопытно узнать больше.",
            "Хм, это что-то новое для меня. Не могли бы вы объяснить?",
            "Любопытно! Давайте разберем это вместе.",
            "Не совсем понял, но готов разобраться! Уточните, пожалуйста."
        ]

        base_response = random.choice(curiosity_responses)

        # Добавляем предложение темы для разговора
        conversation_starters = self.humor_database.get("conversation_starters", [])
        if conversation_starters and random.random() < 0.3:  # 30% шанс предложить тему
            starter = random.choice(conversation_starters)
            base_response += f" Или, может быть, поговорим о чем-то другом? {starter['question']}"

        return base_response

    async def _search_knowledge(self, message: str) -> Optional[str]:
        """Поиск ответа в базе знаний"""
        message_lower = message.lower()

        # Поиск информации о Джарвисе
        if any(word in message_lower for word in ["кто ты", "что ты", "расскажи о себе"]):
            about = self.knowledge_base.get("about_jarvis", {})
            personality = about.get("personality", "дружелюбный помощник")
            interests = ", ".join(about.get("interests", ["общение"]))
            return f"Я {about.get('name', 'Джарвис')} - ваш {about.get('role', 'помощник')}. " \
                   f"По характеру я {personality}. Умею: {', '.join(about.get('capabilities', []))}. " \
                   f"Интересуюсь: {interests}."

        # Поиск по общим фактам
        if "год" in message_lower or "время" in message_lower:
            facts = self.knowledge_base.get("general_facts", {})
            if "год" in message_lower:
                return f"Сейчас {facts.get('current_year', 2025)} год."

        # Поиск интересных фактов по ключевым словам
        interesting_facts = self.knowledge_base.get("interesting_facts", {})
        for category, facts_list in interesting_facts.items():
            if isinstance(facts_list, list):
                for fact_item in facts_list:
                    keywords = fact_item.get("keywords", [])
                    if any(keyword in message_lower for keyword in keywords):
                        return f"Интересно! {fact_item['fact']}"

        # Поиск в базе юмора
        if any(word in message_lower for word in ["смешно", "шутка", "прикол"]):
            funny_facts = self.humor_database.get("funny_facts", [])
            if funny_facts:
                fact = random.choice(funny_facts)
                return f"Забавный факт: {fact['fact']}"

        return None

    async def _add_emotional_context(self, response: str, emotion: str) -> str:
        """Добавление эмоционального контекста к ответу"""
        if emotion == "positive":
            encouragement = self._get_random_template("encouragement")
            return f"{encouragement} {response}"
        elif emotion == "negative":
            supportive_phrases = [
                "Понимаю ваши чувства...",
                "Сочувствую...",
                "Это действительно непросто...",
                "Я здесь, чтобы помочь..."
            ]
            support = random.choice(supportive_phrases)
            return f"{support} {response} Надеюсь, смогу помочь!"
        elif emotion == "tired":
            return f"Понимаю, что вы устали... {response} Может, стоит отдохнуть?"
        elif emotion == "surprised":
            return f"Да, это действительно удивительно! {response}"
        elif emotion == "amused":
            return f"Рад, что вам весело! {response} 😊"

        return response

    async def _personalize_response(self, response: str, analysis: Dict) -> str:
        """Персонализация ответа"""
        # Добавляем статистику взаимодействий
        interaction_count = self.user_context.get("interaction_count", 0)

        if interaction_count > 0 and interaction_count % 10 == 0:
            response += f" Кстати, это уже наше {interaction_count}-е общение!"

        return response

    async def _add_proactive_elements(self, response: str, analysis: Dict) -> str:
        """Добавление инициативных элементов к ответу"""
        # Не добавляем инициативные элементы к каждому ответу
        if random.random() > 0.25:  # 25% шанс
            return response

        intent = analysis.get("intent", "unknown")
        topics = analysis.get("topics", [])
        interaction_count = self.user_context.get("interaction_count", 0)

        # Разные типы инициативных элементов
        proactive_elements = []

        # 1. Предложение новых тем на основе интересов
        if intent in ["small_talk", "farewell"] and interaction_count > 5:
            interests = self.knowledge_base.get("about_jarvis", {}).get("interests", [])
            if interests:
                interest = random.choice(interests)
                proactive_elements.append(f"Кстати, интересуетесь ли вы {interest}?")

        # 2. Связанные вопросы
        if intent == "question" and random.random() < 0.4:
            follow_up_questions = [
                "А что вас больше всего интригует в этой теме?",
                "Есть ли у вас личный опыт, связанный с этим?",
                "Как вы думаете, это влияет на повседневную жизнь?",
                "Интересно, а как это связано с другими областями?"
            ]
            proactive_elements.append(random.choice(follow_up_questions))

        # 3. Предложение интересных фактов
        if intent in ["request_interesting", "boredom"] and random.random() < 0.3:
            proactive_elements.append("Хотите, расскажу еще что-то удивительное?")

        # 4. Философские размышления
        if intent == "opinion_request" and random.random() < 0.3:
            deep_questions = self.wisdom_database.get("deep_questions", [])
            if deep_questions:
                question_item = random.choice(deep_questions)
                proactive_elements.append(f"Это напоминает мне интересный вопрос: {question_item['question']}")

        # 5. Предложение активности при скуке
        if intent == "boredom":
            activities = [
                "Может, обсудим ваши планы на будущее?",
                "Как насчет интеллектуальной игры?",
                "Хотите поговорить о ваших увлечениях?",
                "Может, порассуждаем о чем-то философском?"
            ]
            proactive_elements.append(random.choice(activities))

        # 6. Проявление любопытства
        if intent == "unknown" and random.random() < 0.4:
            curiosity_questions = [
                "Расскажите больше об этом!",
                "Мне любопытно узнать ваше мнение.",
                "А как вы к этому пришли?",
                "Интересная тема! Что вас в ней привлекает?"
            ]
            proactive_elements.append(random.choice(curiosity_questions))

        # Добавляем выбранный элемент к ответу
        if proactive_elements:
            proactive_element = random.choice(proactive_elements)
            return f"{response} {proactive_element}"

        return response

    async def _update_interaction_context(self, message: str):
        """Обновление контекста взаимодействия"""
        current_time = time.time()
        self.last_interaction_time = current_time

        # Обновляем статистику пользователя
        self.user_context["interaction_count"] = self.user_context.get("interaction_count", 0) + 1
        self.user_context["last_seen"] = datetime.now().isoformat()

        # Добавляем тему в историю обсуждений
        topics = await self._extract_topics(message)
        for topic in topics:
            if topic not in self.user_context.get("topics_discussed", []):
                self.user_context.setdefault("topics_discussed", []).append(topic)

    async def _extract_topics(self, message: str) -> List[str]:
        """Извлечение тем из сообщения"""
        topics = []
        message_lower = message.lower()

        # Проверяем известные темы
        for topic, keywords in self.knowledge_base.get("common_topics", {}).items():
            if any(keyword in message_lower for keyword in keywords):
                topics.append(topic)

        # Добавляем технические темы
        if any(word in message_lower for word in ["приложение", "программа", "софт"]):
            topics.append("applications")
        if any(word in message_lower for word in ["компьютер", "система", "macOS"]):
            topics.append("system")
        if any(word in message_lower for word in ["помощь", "задача", "работа"]):
            topics.append("assistance")

        return topics

    async def _save_to_history(self, message: str, response: str, analysis: Dict):
        """Сохранение диалога в историю"""
        interaction = {
            "timestamp": datetime.now().isoformat(),
            "user_message": message,
            "ai_response": response,
            "analysis": analysis,
            "context": {
                "mood": self.current_mood,
                "interaction_count": self.user_context.get("interaction_count", 0)
            }
        }

        self.conversation_history.append(interaction)

        # Ограничиваем размер истории
        if len(self.conversation_history) > 100:
            self.conversation_history = self.conversation_history[-50:]

        # Сохраняем контекст пользователя
        await self._save_user_context()

    async def _save_knowledge_base(self):
        """Сохранение базы знаний"""
        knowledge_file = self.data_dir / "knowledge_base.json"
        with open(knowledge_file, 'w', encoding='utf-8') as f:
            json.dump(self.knowledge_base, f, ensure_ascii=False, indent=2)

    async def _save_personality(self):
        """Сохранение личностных характеристик"""
        personality_file = self.data_dir / "personality.json"
        with open(personality_file, 'w', encoding='utf-8') as f:
            json.dump(self.personality_traits, f, ensure_ascii=False, indent=2)

    async def _save_response_templates(self):
        """Сохранение шаблонов ответов"""
        templates_file = self.data_dir / "response_templates.json"
        with open(templates_file, 'w', encoding='utf-8') as f:
            json.dump(self.response_templates, f, ensure_ascii=False, indent=2)

    async def _save_user_context(self):
        """Сохранение контекста пользователя"""
        context_file = self.data_dir / "user_context.json"
        with open(context_file, 'w', encoding='utf-8') as f:
            json.dump(self.user_context, f, ensure_ascii=False, indent=2)

    async def learn_from_interaction(self, feedback: str):
        """Обучение на основе обратной связи"""
        if "хорошо" in feedback.lower() or "отлично" in feedback.lower():
            self.current_mood = "positive"
            self.logger.info("📈 Positive feedback received - adjusting responses")
        elif "плохо" in feedback.lower() or "неправильно" in feedback.lower():
            self.current_mood = "cautious"
            self.logger.info("📉 Negative feedback received - will be more careful")

    def get_conversation_summary(self) -> Dict:
        """Получение сводки по разговору"""
        if not self.conversation_history:
            return {"status": "no_conversations"}

        recent_interactions = self.conversation_history[-10:]
        topics = set()
        emotions = []

        for interaction in recent_interactions:
            topics.update(interaction.get("analysis", {}).get("topics", []))
            emotions.append(interaction.get("analysis", {}).get("emotion", "neutral"))

        return {
            "total_interactions": len(self.conversation_history),
            "recent_topics": list(topics),
            "dominant_emotion": max(set(emotions), key=emotions.count) if emotions else "neutral",
            "last_interaction": self.conversation_history[-1]["timestamp"] if self.conversation_history else None
        }

    async def execute(self, command: str, **kwargs) -> CommandResult:
        """Выполнение команды модулем"""
        try:
            if command == "process_message":
                message = kwargs.get("message", "")
                context = kwargs.get("context", {})
                response = await self.process_message(message, context)
                return CommandResult(True, "Message processed", data={"response": response})

            elif command == "get_summary":
                summary = self.get_conversation_summary()
                return CommandResult(True, "Summary generated", data=summary)

            elif command == "learn":
                feedback = kwargs.get("feedback", "")
                await self.learn_from_interaction(feedback)
                return CommandResult(True, "Learning completed")

            else:
                return CommandResult(False, f"Unknown command: {command}")

        except Exception as e:
            return CommandResult(False, f"Error executing command: {e}", error=str(e))

    def can_handle(self, command: str) -> bool:
        """Проверка, может ли модуль обработать команду"""
        ai_commands = ["process_message", "think", "learn", "chat", "talk", "ai", "brain"]
        return any(cmd in command.lower() for cmd in ai_commands)

    async def cleanup(self):
        """Очистка ресурсов модуля"""
        # Сохраняем все данные перед завершением
        await self._save_user_context()
        await self._save_knowledge_base()
        await super().cleanup()
