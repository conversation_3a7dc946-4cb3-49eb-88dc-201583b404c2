"""
Модуль искусственного интеллекта для естественного общения Jarvis
"""

import asyncio
import json
import re
import random
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from jarvis.core.base import BaseModule, CommandResult
from jarvis.core.logger import jarvis_logger
from jarvis.config import config

class AIBrain(BaseModule):
    """Модуль ИИ для естественного общения и понимания контекста"""
    
    def __init__(self):
        super().__init__("AIBrain")
        
        # Память и контекст
        self.conversation_history = []
        self.user_context = {}
        self.current_mood = "neutral"
        self.last_interaction_time = 0
        
        # База знаний
        self.knowledge_base = {}
        self.personality_traits = {}
        self.response_templates = {}
        
        # Пути к файлам данных
        self.data_dir = config.project_root / "data" / "ai_brain"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
    async def initialize(self) -> bool:
        """Инициализация ИИ модуля"""
        try:
            self.logger.info("Initializing AI Brain Module...")
            
            # Загружаем базу знаний
            await self._load_knowledge_base()
            
            # Загружаем личность
            await self._load_personality()
            
            # Загружаем шаблоны ответов
            await self._load_response_templates()
            
            # Загружаем контекст пользователя
            await self._load_user_context()
            
            self.logger.info("🧠 AI Brain Module initialized successfully")
            self.is_initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AI Brain Module: {e}")
            return False
    
    async def _load_knowledge_base(self):
        """Загрузка базы знаний"""
        knowledge_file = self.data_dir / "knowledge_base.json"
        
        default_knowledge = {
            "about_jarvis": {
                "name": "Джарвис",
                "role": "персональный ИИ-помощник",
                "creator": "пользователь",
                "capabilities": [
                    "голосовое управление",
                    "открытие приложений",
                    "естественное общение",
                    "помощь в задачах"
                ]
            },
            "general_facts": {
                "current_year": 2025,
                "system": "macOS",
                "language": "русский"
            },
            "common_topics": {
                "greeting": ["привет", "здравствуй", "добро пожаловать"],
                "farewell": ["пока", "до свидания", "увидимся"],
                "gratitude": ["спасибо", "благодарю", "признателен"],
                "weather": ["погода", "температура", "дождь", "солнце"],
                "time": ["время", "час", "минута", "сейчас"]
            }
        }
        
        if knowledge_file.exists():
            with open(knowledge_file, 'r', encoding='utf-8') as f:
                self.knowledge_base = json.load(f)
        else:
            self.knowledge_base = default_knowledge
            await self._save_knowledge_base()
    
    async def _load_personality(self):
        """Загрузка личностных характеристик"""
        personality_file = self.data_dir / "personality.json"
        
        default_personality = {
            "traits": {
                "helpful": 0.9,
                "friendly": 0.8,
                "professional": 0.7,
                "humorous": 0.6,
                "curious": 0.7,
                "patient": 0.8
            },
            "communication_style": {
                "formality": "умеренная",
                "verbosity": "средняя",
                "emoji_usage": "умеренное",
                "humor_type": "легкий"
            },
            "preferences": {
                "response_length": "medium",
                "explanation_detail": "balanced",
                "proactive_suggestions": True
            }
        }
        
        if personality_file.exists():
            with open(personality_file, 'r', encoding='utf-8') as f:
                self.personality_traits = json.load(f)
        else:
            self.personality_traits = default_personality
            await self._save_personality()
    
    async def _load_response_templates(self):
        """Загрузка шаблонов ответов"""
        templates_file = self.data_dir / "response_templates.json"
        
        default_templates = {
            "greeting": [
                "Привет! Как дела?",
                "Здравствуйте! Чем могу помочь?",
                "Добро пожаловать! Готов к работе!",
                "Привет! Рад вас видеть!"
            ],
            "farewell": [
                "До свидания! Хорошего дня!",
                "Пока! Обращайтесь, если что-то понадобится!",
                "До встречи! Удачи!",
                "Всего доброго!"
            ],
            "gratitude_response": [
                "Пожалуйста! Всегда рад помочь!",
                "Не за что! Это моя работа!",
                "Обращайтесь! Я здесь для этого!",
                "Рад был помочь!"
            ],
            "unknown": [
                "Интересно... Расскажите подробнее!",
                "Не совсем понял, но готов разобраться!",
                "Хм, это что-то новое для меня. Объясните?",
                "Любопытно! Давайте обсудим это!"
            ],
            "thinking": [
                "Дайте подумать...",
                "Интересный вопрос...",
                "Размышляю над этим...",
                "Хороший вопрос, анализирую..."
            ],
            "encouragement": [
                "Отлично!",
                "Замечательно!",
                "Прекрасно!",
                "Великолепно!"
            ]
        }
        
        if templates_file.exists():
            with open(templates_file, 'r', encoding='utf-8') as f:
                self.response_templates = json.load(f)
        else:
            self.response_templates = default_templates
            await self._save_response_templates()
    
    async def _load_user_context(self):
        """Загрузка контекста пользователя"""
        context_file = self.data_dir / "user_context.json"
        
        if context_file.exists():
            with open(context_file, 'r', encoding='utf-8') as f:
                self.user_context = json.load(f)
        else:
            self.user_context = {
                "name": None,
                "preferences": {},
                "interaction_count": 0,
                "last_seen": None,
                "topics_discussed": [],
                "mood_history": []
            }
    
    async def process_message(self, message: str, context: Dict = None) -> str:
        """Основной метод обработки сообщений"""
        try:
            self.logger.info(f"🧠 Processing message: '{message}'")
            
            # Обновляем контекст
            await self._update_interaction_context(message)
            
            # Анализируем сообщение
            analysis = await self._analyze_message(message)
            
            # Генерируем ответ
            response = await self._generate_response(message, analysis, context)
            
            # Сохраняем в историю
            await self._save_to_history(message, response, analysis)
            
            self.logger.info(f"🗣️ Generated response: '{response}'")
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            return "Извините, произошла ошибка в обработке вашего сообщения."
    
    async def _analyze_message(self, message: str) -> Dict:
        """Анализ сообщения для понимания намерений и эмоций"""
        message_lower = message.lower()
        
        analysis = {
            "intent": "unknown",
            "emotion": "neutral",
            "topics": [],
            "entities": [],
            "question_type": None,
            "urgency": "normal"
        }
        
        # Определяем намерение
        if any(word in message_lower for word in ["привет", "здравствуй", "добрый"]):
            analysis["intent"] = "greeting"
        elif any(word in message_lower for word in ["пока", "до свидания", "увидимся"]):
            analysis["intent"] = "farewell"
        elif any(word in message_lower for word in ["спасибо", "благодарю"]):
            analysis["intent"] = "gratitude"
        elif any(word in message_lower for word in ["как дела", "как жизнь", "что нового"]):
            analysis["intent"] = "small_talk"
        elif "?" in message or any(word in message_lower for word in ["что", "как", "где", "когда", "почему"]):
            analysis["intent"] = "question"
            analysis["question_type"] = self._detect_question_type(message_lower)
        elif any(word in message_lower for word in ["открой", "запусти", "закрой"]):
            analysis["intent"] = "command"
        
        # Определяем эмоцию (расширенный анализ)
        if any(word in message_lower for word in ["отлично", "супер", "классно", "здорово", "прекрасно", "замечательно", "восхитительно", "круто"]):
            analysis["emotion"] = "positive"
        elif any(word in message_lower for word in ["плохо", "грустно", "ужасно", "проблема", "печально", "расстроен", "злой", "сердитый"]):
            analysis["emotion"] = "negative"
        elif any(word in message_lower for word in ["устал", "скучно", "надоело", "лень"]):
            analysis["emotion"] = "tired"
        elif any(word in message_lower for word in ["удивительно", "вау", "невероятно", "поразительно"]):
            analysis["emotion"] = "surprised"
        elif any(word in message_lower for word in ["смешно", "ха-ха", "прикольно", "забавно"]):
            analysis["emotion"] = "amused"
        elif any(word in message_lower for word in ["!!", "!!!", "срочно", "быстро"]):
            analysis["urgency"] = "high"
        
        # Извлекаем темы
        for topic, keywords in self.knowledge_base.get("common_topics", {}).items():
            if any(keyword in message_lower for keyword in keywords):
                analysis["topics"].append(topic)
        
        return analysis

    def _detect_question_type(self, message: str) -> str:
        """Определение типа вопроса"""
        if any(word in message for word in ["что", "какой", "какая"]):
            return "what"
        elif any(word in message for word in ["как", "каким образом"]):
            return "how"
        elif any(word in message for word in ["где", "куда"]):
            return "where"
        elif any(word in message for word in ["когда", "во сколько"]):
            return "when"
        elif any(word in message for word in ["почему", "зачем"]):
            return "why"
        elif any(word in message for word in ["кто", "чей"]):
            return "who"
        return "general"

    async def _generate_response(self, message: str, analysis: Dict, context: Dict = None) -> str:
        """Генерация ответа на основе анализа"""
        intent = analysis["intent"]
        emotion = analysis["emotion"]

        # Базовые ответы по намерениям
        if intent == "greeting":
            response = self._get_random_template("greeting")
            # Персонализируем приветствие
            if self.user_context.get("name"):
                response = f"{response.split('!')[0]}, {self.user_context['name']}!"

        elif intent == "farewell":
            response = self._get_random_template("farewell")

        elif intent == "gratitude":
            response = self._get_random_template("gratitude_response")

        elif intent == "small_talk":
            response = await self._handle_small_talk(message, analysis)

        elif intent == "question":
            response = await self._handle_question(message, analysis)

        elif intent == "command":
            response = "Понял! Выполняю команду..."

        else:
            response = await self._handle_unknown(message, analysis)

        # Добавляем эмоциональную окраску
        response = await self._add_emotional_context(response, emotion)

        # Добавляем персональные элементы
        response = await self._personalize_response(response, analysis)

        return response

    def _get_random_template(self, template_type: str) -> str:
        """Получение случайного шаблона ответа"""
        templates = self.response_templates.get(template_type, ["Понял!"])
        return random.choice(templates)

    async def _handle_small_talk(self, message: str, analysis: Dict) -> str:
        """Обработка светской беседы"""
        responses = [
            "У меня все отлично! Работаю и помогаю вам. А у вас как дела?",
            "Прекрасно! Готов к новым задачам. Что планируете?",
            "Все хорошо! Изучаю новое и совершенствуюсь. А вы как?",
            "Замечательно! Рад нашему общению. Чем займемся?"
        ]

        base_response = random.choice(responses)

        # Добавляем контекст времени
        current_hour = datetime.now().hour
        if 6 <= current_hour < 12:
            time_context = " Доброе утро, кстати!"
        elif 12 <= current_hour < 18:
            time_context = " Хорошего дня!"
        elif 18 <= current_hour < 22:
            time_context = " Приятного вечера!"
        else:
            time_context = " Поздновато работаете!"

        return base_response + time_context

    async def _handle_question(self, message: str, analysis: Dict) -> str:
        """Обработка вопросов"""
        question_type = analysis.get("question_type", "general")

        # Проверяем, есть ли ответ в базе знаний
        knowledge_response = await self._search_knowledge(message)
        if knowledge_response:
            return knowledge_response

        # Генерируем ответ в зависимости от типа вопроса
        thinking_phrase = self._get_random_template("thinking")

        if question_type == "what":
            return f"{thinking_phrase} Это интересный вопрос! Расскажите, в каком контексте вас это интересует?"
        elif question_type == "how":
            return f"{thinking_phrase} Хороший вопрос! Я могу помочь разобраться. Уточните детали?"
        elif question_type == "when":
            return f"Что касается времени... Нужно больше информации для точного ответа."
        elif question_type == "where":
            return f"По поводу местоположения... Можете уточнить, что именно ищете?"
        elif question_type == "why":
            return f"{thinking_phrase} Философский вопрос! Давайте разберем это вместе."
        else:
            return f"{thinking_phrase} Интересно! Помогите мне понять вопрос лучше."

    async def _handle_unknown(self, message: str, analysis: Dict) -> str:
        """Обработка неизвестных сообщений"""
        base_response = self._get_random_template("unknown")

        # Пытаемся найти ключевые слова для контекста
        if any(word in message.lower() for word in ["помощь", "помоги", "нужно"]):
            return "Конечно, помогу! Расскажите подробнее, что нужно сделать?"
        elif any(word in message.lower() for word in ["расскажи", "объясни"]):
            return "С удовольствием расскажу! О чем именно хотите узнать?"
        elif any(word in message.lower() for word in ["можешь", "умеешь"]):
            return "Я умею многое! Голосовое управление, открытие приложений, общение. Что попробуем?"

        return base_response

    async def _search_knowledge(self, message: str) -> Optional[str]:
        """Поиск ответа в базе знаний"""
        message_lower = message.lower()

        # Поиск информации о Джарвисе
        if any(word in message_lower for word in ["кто ты", "что ты", "расскажи о себе"]):
            about = self.knowledge_base.get("about_jarvis", {})
            return f"Я {about.get('name', 'Джарвис')} - ваш {about.get('role', 'помощник')}. " \
                   f"Умею: {', '.join(about.get('capabilities', []))}."

        # Поиск по общим фактам
        if "год" in message_lower or "время" in message_lower:
            facts = self.knowledge_base.get("general_facts", {})
            if "год" in message_lower:
                return f"Сейчас {facts.get('current_year', 2025)} год."

        return None

    async def _add_emotional_context(self, response: str, emotion: str) -> str:
        """Добавление эмоционального контекста к ответу"""
        if emotion == "positive":
            encouragement = self._get_random_template("encouragement")
            return f"{encouragement} {response}"
        elif emotion == "negative":
            supportive_phrases = [
                "Понимаю ваши чувства...",
                "Сочувствую...",
                "Это действительно непросто...",
                "Я здесь, чтобы помочь..."
            ]
            support = random.choice(supportive_phrases)
            return f"{support} {response} Надеюсь, смогу помочь!"
        elif emotion == "tired":
            return f"Понимаю, что вы устали... {response} Может, стоит отдохнуть?"
        elif emotion == "surprised":
            return f"Да, это действительно удивительно! {response}"
        elif emotion == "amused":
            return f"Рад, что вам весело! {response} 😊"

        return response

    async def _personalize_response(self, response: str, analysis: Dict) -> str:
        """Персонализация ответа"""
        # Добавляем статистику взаимодействий
        interaction_count = self.user_context.get("interaction_count", 0)

        if interaction_count > 0 and interaction_count % 10 == 0:
            response += f" Кстати, это уже наше {interaction_count}-е общение!"

        return response

    async def _update_interaction_context(self, message: str):
        """Обновление контекста взаимодействия"""
        current_time = time.time()
        self.last_interaction_time = current_time

        # Обновляем статистику пользователя
        self.user_context["interaction_count"] = self.user_context.get("interaction_count", 0) + 1
        self.user_context["last_seen"] = datetime.now().isoformat()

        # Добавляем тему в историю обсуждений
        topics = await self._extract_topics(message)
        for topic in topics:
            if topic not in self.user_context.get("topics_discussed", []):
                self.user_context.setdefault("topics_discussed", []).append(topic)

    async def _extract_topics(self, message: str) -> List[str]:
        """Извлечение тем из сообщения"""
        topics = []
        message_lower = message.lower()

        # Проверяем известные темы
        for topic, keywords in self.knowledge_base.get("common_topics", {}).items():
            if any(keyword in message_lower for keyword in keywords):
                topics.append(topic)

        # Добавляем технические темы
        if any(word in message_lower for word in ["приложение", "программа", "софт"]):
            topics.append("applications")
        if any(word in message_lower for word in ["компьютер", "система", "macOS"]):
            topics.append("system")
        if any(word in message_lower for word in ["помощь", "задача", "работа"]):
            topics.append("assistance")

        return topics

    async def _save_to_history(self, message: str, response: str, analysis: Dict):
        """Сохранение диалога в историю"""
        interaction = {
            "timestamp": datetime.now().isoformat(),
            "user_message": message,
            "ai_response": response,
            "analysis": analysis,
            "context": {
                "mood": self.current_mood,
                "interaction_count": self.user_context.get("interaction_count", 0)
            }
        }

        self.conversation_history.append(interaction)

        # Ограничиваем размер истории
        if len(self.conversation_history) > 100:
            self.conversation_history = self.conversation_history[-50:]

        # Сохраняем контекст пользователя
        await self._save_user_context()

    async def _save_knowledge_base(self):
        """Сохранение базы знаний"""
        knowledge_file = self.data_dir / "knowledge_base.json"
        with open(knowledge_file, 'w', encoding='utf-8') as f:
            json.dump(self.knowledge_base, f, ensure_ascii=False, indent=2)

    async def _save_personality(self):
        """Сохранение личностных характеристик"""
        personality_file = self.data_dir / "personality.json"
        with open(personality_file, 'w', encoding='utf-8') as f:
            json.dump(self.personality_traits, f, ensure_ascii=False, indent=2)

    async def _save_response_templates(self):
        """Сохранение шаблонов ответов"""
        templates_file = self.data_dir / "response_templates.json"
        with open(templates_file, 'w', encoding='utf-8') as f:
            json.dump(self.response_templates, f, ensure_ascii=False, indent=2)

    async def _save_user_context(self):
        """Сохранение контекста пользователя"""
        context_file = self.data_dir / "user_context.json"
        with open(context_file, 'w', encoding='utf-8') as f:
            json.dump(self.user_context, f, ensure_ascii=False, indent=2)

    async def learn_from_interaction(self, feedback: str):
        """Обучение на основе обратной связи"""
        if "хорошо" in feedback.lower() or "отлично" in feedback.lower():
            self.current_mood = "positive"
            self.logger.info("📈 Positive feedback received - adjusting responses")
        elif "плохо" in feedback.lower() or "неправильно" in feedback.lower():
            self.current_mood = "cautious"
            self.logger.info("📉 Negative feedback received - will be more careful")

    def get_conversation_summary(self) -> Dict:
        """Получение сводки по разговору"""
        if not self.conversation_history:
            return {"status": "no_conversations"}

        recent_interactions = self.conversation_history[-10:]
        topics = set()
        emotions = []

        for interaction in recent_interactions:
            topics.update(interaction.get("analysis", {}).get("topics", []))
            emotions.append(interaction.get("analysis", {}).get("emotion", "neutral"))

        return {
            "total_interactions": len(self.conversation_history),
            "recent_topics": list(topics),
            "dominant_emotion": max(set(emotions), key=emotions.count) if emotions else "neutral",
            "last_interaction": self.conversation_history[-1]["timestamp"] if self.conversation_history else None
        }

    async def execute(self, command: str, **kwargs) -> CommandResult:
        """Выполнение команды модулем"""
        try:
            if command == "process_message":
                message = kwargs.get("message", "")
                context = kwargs.get("context", {})
                response = await self.process_message(message, context)
                return CommandResult(True, "Message processed", data={"response": response})

            elif command == "get_summary":
                summary = self.get_conversation_summary()
                return CommandResult(True, "Summary generated", data=summary)

            elif command == "learn":
                feedback = kwargs.get("feedback", "")
                await self.learn_from_interaction(feedback)
                return CommandResult(True, "Learning completed")

            else:
                return CommandResult(False, f"Unknown command: {command}")

        except Exception as e:
            return CommandResult(False, f"Error executing command: {e}", error=str(e))

    def can_handle(self, command: str) -> bool:
        """Проверка, может ли модуль обработать команду"""
        ai_commands = ["process_message", "think", "learn", "chat", "talk", "ai", "brain"]
        return any(cmd in command.lower() for cmd in ai_commands)

    async def cleanup(self):
        """Очистка ресурсов модуля"""
        # Сохраняем все данные перед завершением
        await self._save_user_context()
        await self._save_knowledge_base()
        await super().cleanup()
