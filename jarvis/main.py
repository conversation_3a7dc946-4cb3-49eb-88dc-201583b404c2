"""
Главн<PERSON>й модуль Jarvis AI Assistant
"""

import asyncio
import signal
import sys
from typing import List
from jarvis.core.logger import jarvis_logger
from jarvis.core.base import BaseModule
from jarvis.config import config
from jarvis.modules.voice import VoiceModule
from jarvis.modules.command_processor import LocalCommandProcessor
from jarvis.modules.app_manager import AppManager
from jarvis.modules.tts import TTSModule
from jarvis.modules.ai_brain import AIBrain

class JarvisCore:
    """Основной класс Jarvis AI Assistant"""
    
    def __init__(self):
        self.logger = jarvis_logger
        self.modules: List[BaseModule] = []
        self.is_running = False
        
    async def initialize(self):
        """Инициализация Jarvis"""
        self.logger.info(f"Initializing {config.jarvis_name} AI Assistant...")
        
        # Инициализируем модули
        self.voice_module = VoiceModule()
        self.command_processor = LocalCommandProcessor()
        self.app_manager = AppManager()
        self.tts_module = TTSModule()
        self.ai_brain = AIBrain()

        self.modules = [
            self.voice_module,
            self.command_processor,
            self.app_manager,
            self.tts_module,
            self.ai_brain,
            # TODO: Добавим остальные модули по мере их создания
            # TelegramModule(),
            # SystemModule(),
            # FileModule(),
            # WebModule()
        ]
        
        # Инициализируем все модули
        for module in self.modules:
            try:
                success = await module.initialize()
                if success:
                    self.logger.info(f"Module {module.name} initialized successfully")
                else:
                    self.logger.error(f"Failed to initialize module {module.name}")
            except Exception as e:
                self.logger.error(f"Error initializing module {module.name}: {e}")
        
        self.logger.info(f"{config.jarvis_name} initialization complete!")
    
    async def start(self):
        """Запуск Jarvis"""
        await self.initialize()
        self.is_running = True
        
        self.logger.info(f"{config.jarvis_name} is now listening...")
        self.logger.info(f"Say '{config.wake_word}' to activate")

        # Запускаем голосовое прослушивание
        if self.voice_module.is_initialized:
            await self.voice_module.start_listening()

        try:
            while self.is_running:
                # Проверяем очередь голосовых команд
                if self.voice_module.is_initialized:
                    command = self.voice_module.get_command()
                    if command:
                        await self._handle_voice_command(command)

                await asyncio.sleep(0.1)

        except KeyboardInterrupt:
            self.logger.info("Received interrupt signal")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Завершение работы Jarvis"""
        self.logger.info(f"Shutting down {config.jarvis_name}...")
        self.is_running = False
        
        # Очищаем ресурсы модулей
        for module in self.modules:
            try:
                await module.cleanup()
            except Exception as e:
                self.logger.error(f"Error cleaning up module {module.name}: {e}")
        
        self.logger.info(f"{config.jarvis_name} shutdown complete")

    async def _handle_voice_command(self, command: str):
        """Обработка голосовых команд с ИИ"""
        self.logger.info(f"🎤 Processing voice command: '{command}'")

        try:
            # Специальная обработка активации wake word
            if command == "__WAKE_WORD_ACTIVATED__":
                response = "Да, слушаю вас! Можете говорить без упоминания моего имени. Буду слушать пока вы не замолчите на 10 секунд."
                self.logger.info(f"👂 {response}")
                await self._speak_with_mute(response)
                return

            # Обработка остановки диалога
            elif command == "__DIALOG_STOPPED__":
                response = "Хорошо, завершаю активный диалог. Чтобы снова обратиться ко мне, скажите 'Джарвис'."
                self.logger.info(f"🛑 {response}")
                await self._speak_with_mute(response)
                return

            # Обработка таймаута диалога
            elif command == "__DIALOG_TIMEOUT__":
                response = "Вы молчали 10 секунд, завершаю диалог. Чтобы снова обратиться ко мне, скажите 'Джарвис'."
                self.logger.info(f"⏰ {response}")
                await self._speak_with_mute(response)
                return

            # Сначала пробуем обработать через ИИ для естественного общения
            if self.ai_brain.is_initialized:
                # Проверяем, является ли это командой или обычным общением
                is_system_command = await self._is_system_command(command)

                if not is_system_command:
                    # Обрабатываем как естественное общение через ИИ
                    ai_response = await self.ai_brain.process_message(command)
                    self.logger.info(f"🧠 AI Response: {ai_response}")
                    await self._speak_with_mute(ai_response)
                    return

            # Парсим команду через локальный процессор для системных команд
            parsed_result = await self.command_processor.execute(command)
            if not parsed_result.success:
                # Если команда не распознана, отправляем в ИИ
                if self.ai_brain.is_initialized:
                    ai_response = await self.ai_brain.process_message(command)
                    await self._speak_with_mute(ai_response)
                else:
                    await self._speak_with_mute("Извините, я не понял команду.")
                return

            parsed_command = parsed_result.data
            intent = parsed_command["intent"]
            entities = parsed_command["entities"]

            self.logger.info(f"🧠 Detected intent: {intent}")

            # Обрабатываем системные команды
            await self._handle_system_command(intent, entities, command)

        except Exception as e:
            self.logger.error(f"Error processing voice command: {e}")

    async def _is_system_command(self, command: str) -> bool:
        """Проверка, является ли команда системной"""
        system_keywords = [
            "открой", "закрой", "запусти", "выключи", "включи",
            "стоп", "остановись", "заверши", "выход", "помощь"
        ]
        command_lower = command.lower()
        return any(keyword in command_lower for keyword in system_keywords)

    async def _handle_system_command(self, intent: str, entities: list, original_command: str):
        """Обработка системных команд"""
        # Обрабатываем команды по намерениям
        if intent == "greeting":
            # Даже приветствие обрабатываем через ИИ для более естественного ответа
            if self.ai_brain.is_initialized:
                response = await self.ai_brain.process_message(original_command)
            else:
                response = "Привет! Я ваш персональный помощник Джарвис!"
            await self._speak_with_mute(response)

        elif intent == "goodbye":
            if self.ai_brain.is_initialized:
                response = await self.ai_brain.process_message(original_command)
            else:
                response = "До свидания! Завершаю работу."
            await self._speak_with_mute(response)
            self.is_running = False

        elif intent == "test":
            response = "Все системы работают нормально! ИИ модуль активен и готов к общению!"
            await self._speak_with_mute(response)

        elif intent == "stop":
            response = "Остановка по команде пользователя"
            await self._speak_with_mute(response)
            self.is_running = False

        elif intent == "help":
            dialog_status = "активен" if self.voice_module.is_in_dialog_mode() else "неактивен"
            response = f"Теперь я умею общаться в режиме диалога! Режим диалога сейчас {dialog_status}. Попробуйте просто поговорить со мной."
            await self._speak_with_mute(response)
            self._show_help()

        elif intent == "app_open" and entities:
            app_name = self.command_processor.extract_app_name(entities[0])
            self.logger.info(f"🚀 Opening application: {app_name}")
            result = await self.app_manager.open_application(app_name)
            if result.success:
                response = f"Открываю {app_name}"
                await self._speak_with_mute(response)
            else:
                response = f"Не могу найти приложение {app_name}"
                await self._speak_with_mute(response)

        elif intent == "app_close" and entities:
            app_name = self.command_processor.extract_app_name(entities[0])
            self.logger.info(f"🔴 Closing application: {app_name}")
            result = await self.app_manager.close_application(app_name)
            if result.success:
                response = f"Закрываю {app_name}"
                await self._speak_with_mute(response)
            else:
                response = f"Не могу закрыть {app_name}"
                await self._speak_with_mute(response)

        else:
            # Неизвестная команда - отправляем в ИИ
            if self.ai_brain.is_initialized:
                response = await self.ai_brain.process_message(original_command)
                await self._speak_with_mute(response)
            else:
                response = f"Команда {intent} пока не реализована"
                await self._speak_with_mute(response)

    async def _speak_with_mute(self, text: str):
        """Воспроизведение TTS с временным отключением микрофона"""
        try:
            # Отключаем микрофон
            if self.voice_module.is_initialized:
                self.voice_module.set_speaking_state(True)

            # Дополнительная пауза перед началом речи
            await asyncio.sleep(0.2)

            # Воспроизводим речь
            await self.tts_module.speak(text)

            # Увеличенная пауза после воспроизведения для полного завершения
            await asyncio.sleep(1.5)

        finally:
            # Включаем микрофон обратно
            if self.voice_module.is_initialized:
                self.voice_module.set_speaking_state(False)
                # Дополнительная пауза после включения микрофона
                await asyncio.sleep(0.3)

    def _show_help(self):
        """Показать справку по командам"""
        self.logger.info("💡 Доступные возможности:")
        self.logger.info("   🎯 Активация:")
        self.logger.info("      • 'Джарвис' - активировать диалог")
        self.logger.info("      • После активации можно говорить без имени!")
        self.logger.info("   💬 Режим диалога:")
        self.logger.info("      • Активен пока вы говорите")
        self.logger.info("      • Завершается после 10 секунд тишины")
        self.logger.info("      • 'Стоп' - завершить диалог досрочно")
        self.logger.info("   🗣️ Естественное общение:")
        self.logger.info("      • 'Как дела?' - поговорить")
        self.logger.info("      • 'Расскажи о себе' - узнать больше")
        self.logger.info("      • 'Что умеешь?' - узнать возможности")
        self.logger.info("   📱 Системные команды:")
        self.logger.info("      • 'Открой [приложение]' - запустить приложение")
        self.logger.info("      • 'Закрой [приложение]' - закрыть приложение")
        self.logger.info("      • 'Тест' - проверить работу")
        self.logger.info("      • 'Помощь' - показать эту справку")

async def main():
    """Главная функция"""
    jarvis = JarvisCore()
    
    # Обработка сигналов для корректного завершения
    def signal_handler(signum, frame):
        jarvis.logger.info(f"Received signal {signum}")
        jarvis.is_running = False
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    await jarvis.start()

if __name__ == "__main__":
    asyncio.run(main())
