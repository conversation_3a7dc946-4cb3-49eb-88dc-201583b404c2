#!/usr/bin/env python3
"""
Тест улучшенного ИИ модуля Джарвиса
Проверяет новые возможности: интересные факты, юмор, советы, философские размышления
"""

import asyncio
import sys
import os
from pathlib import Path

# Добавляем путь к модулям Jarvis
sys.path.append(str(Path(__file__).parent))

from jarvis.modules.ai_brain import AIBrain
from jarvis.config import config

class AITester:
    def __init__(self):
        self.ai_brain = AIBrain()
        self.test_results = []
    
    async def setup(self):
        """Инициализация ИИ модуля"""
        print("🔧 Инициализация ИИ модуля...")
        success = await self.ai_brain.initialize()
        if not success:
            print("❌ Ошибка инициализации!")
            return False
        print("✅ ИИ модуль инициализирован")
        return True
    
    async def test_basic_conversation(self):
        """Тест базового общения"""
        print("\n📝 Тест базового общения:")
        
        test_messages = [
            "Привет!",
            "Как дела?",
            "Спасибо за помощь",
            "До свидания"
        ]
        
        for message in test_messages:
            response = await self.ai_brain.process_message(message)
            print(f"👤 {message}")
            print(f"🤖 {response}")
            print()
    
    async def test_interesting_facts(self):
        """Тест интересных фактов"""
        print("\n🔬 Тест интересных фактов:")
        
        test_messages = [
            "Расскажи что-нибудь интересное",
            "Расскажи интересное о космосе",
            "Удиви меня фактом о науке",
            "Что интересного о природе?"
        ]
        
        for message in test_messages:
            response = await self.ai_brain.process_message(message)
            print(f"👤 {message}")
            print(f"🤖 {response}")
            print()
    
    async def test_humor(self):
        """Тест юмора"""
        print("\n😄 Тест юмора:")
        
        test_messages = [
            "Расскажи шутку",
            "Рассмеши меня",
            "Знаешь анекдоты про программистов?",
            "Что-то смешное про науку"
        ]
        
        for message in test_messages:
            response = await self.ai_brain.process_message(message)
            print(f"👤 {message}")
            print(f"🤖 {response}")
            print()
    
    async def test_advice(self):
        """Тест советов"""
        print("\n💡 Тест советов:")
        
        test_messages = [
            "Дай совет",
            "Что делать, если грустно?",
            "Как поступить в сложной ситуации?",
            "Помоги принять решение"
        ]
        
        for message in test_messages:
            response = await self.ai_brain.process_message(message)
            print(f"👤 {message}")
            print(f"🤖 {response}")
            print()
    
    async def test_boredom(self):
        """Тест обработки скуки"""
        print("\n😴 Тест обработки скуки:")
        
        test_messages = [
            "Мне скучно",
            "Нечем заняться",
            "Развлеки меня"
        ]
        
        for message in test_messages:
            response = await self.ai_brain.process_message(message)
            print(f"👤 {message}")
            print(f"🤖 {response}")
            print()
    
    async def test_opinions(self):
        """Тест запросов мнения"""
        print("\n🤔 Тест запросов мнения:")
        
        test_messages = [
            "Что думаешь о технологиях?",
            "Какое твое мнение о времени?",
            "Что считаешь самым важным в жизни?"
        ]
        
        for message in test_messages:
            response = await self.ai_brain.process_message(message)
            print(f"👤 {message}")
            print(f"🤖 {response}")
            print()
    
    async def test_questions(self):
        """Тест вопросов"""
        print("\n❓ Тест вопросов:")
        
        test_messages = [
            "Что такое счастье?",
            "Как работает мозг?",
            "Почему небо голубое?",
            "Кто ты такой?"
        ]
        
        for message in test_messages:
            response = await self.ai_brain.process_message(message)
            print(f"👤 {message}")
            print(f"🤖 {response}")
            print()
    
    async def test_emotional_responses(self):
        """Тест эмоциональных ответов"""
        print("\n😊 Тест эмоциональных ответов:")
        
        test_messages = [
            "У меня отличное настроение!",
            "Мне грустно сегодня",
            "Я устал",
            "Это просто невероятно!"
        ]
        
        for message in test_messages:
            response = await self.ai_brain.process_message(message)
            print(f"👤 {message}")
            print(f"🤖 {response}")
            print()
    
    async def test_reasoning(self):
        """Тест системы рассуждений"""
        print("\n🧠 Тест системы рассуждений:")

        test_messages = [
            "Проанализируй эту ситуацию",
            "Рассуди логично",
            "Сравни варианты",
            "Что лучше: A или B?",
            "Разбери за и против"
        ]

        for message in test_messages:
            response = await self.ai_brain.process_message(message)
            print(f"👤 {message}")
            print(f"🤖 {response}")
            print()

    async def test_proactive_communication(self):
        """Тест инициативного общения"""
        print("\n🎯 Тест инициативного общения:")

        test_messages = [
            "Привет",
            "Как дела?",
            "Расскажи что-то интересное",
            "Мне скучно",
            "Что думаешь о жизни?"
        ]

        for message in test_messages:
            response = await self.ai_brain.process_message(message)
            print(f"👤 {message}")
            print(f"🤖 {response}")
            print()

    async def test_unknown_messages(self):
        """Тест неизвестных сообщений"""
        print("\n❓ Тест неизвестных сообщений:")

        test_messages = [
            "Бла-бла-бла",
            "Квантовая флуктуация",
            "Помоги мне",
            "Можешь что-то сделать?"
        ]

        for message in test_messages:
            response = await self.ai_brain.process_message(message)
            print(f"👤 {message}")
            print(f"🤖 {response}")
            print()
    
    async def run_all_tests(self):
        """Запуск всех тестов"""
        print("🚀 Запуск тестов улучшенного ИИ Джарвиса")
        print("=" * 50)
        
        if not await self.setup():
            return
        
        # Запускаем все тесты
        await self.test_basic_conversation()
        await self.test_interesting_facts()
        await self.test_humor()
        await self.test_advice()
        await self.test_boredom()
        await self.test_opinions()
        await self.test_reasoning()
        await self.test_proactive_communication()
        await self.test_questions()
        await self.test_emotional_responses()
        await self.test_unknown_messages()
        
        print("\n" + "=" * 50)
        print("✅ Все тесты завершены!")
        
        # Показываем статистику
        summary = self.ai_brain.get_conversation_summary()
        print(f"\n📊 Статистика:")
        print(f"Всего взаимодействий: {summary.get('total_interactions', 0)}")
        print(f"Обсуждаемые темы: {', '.join(summary.get('recent_topics', []))}")
        print(f"Преобладающая эмоция: {summary.get('dominant_emotion', 'neutral')}")

async def main():
    """Главная функция"""
    tester = AITester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
