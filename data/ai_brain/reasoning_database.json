{"reasoning_patterns": {"problem_solving": [{"pattern": "analyze_situation", "steps": ["Давайте разберем ситуацию по частям.", "Сначала определим основную проблему.", "Затем рассмотрим возможные причины.", "И наконец, подумаем о решениях."], "keywords": ["проблема", "ситуация", "сложность", "задача"]}, {"pattern": "pros_and_cons", "steps": ["Рассмотрим это с разных сторон.", "С одной стороны...", "С другой стороны...", "Взвесив все за и против, я думаю..."], "keywords": ["выбор", "решение", "варианты", "альтернативы"]}, {"pattern": "cause_and_effect", "steps": ["Интересно проследить причинно-следственные связи.", "Если мы рассмотрим причины...", "То логично предположить, что следствием будет...", "Это может привести к..."], "keywords": ["почему", "причина", "следствие", "результат"]}], "philosophical_reasoning": [{"pattern": "socratic_method", "steps": ["Это поднимает интересные вопросы.", "А что если мы спросим себя...", "Это заставляет задуматься о...", "Возможно, ответ кроется в..."], "keywords": ["смысл", "философия", "мышление", "понимание"]}, {"pattern": "perspective_shift", "steps": ["Попробуем взглянуть на это под другим углом.", "Если изменить точку зрения...", "Представьте, что...", "Это открывает новые возможности для понимания."], "keywords": ["точка зрения", "перспектива", "взгляд", "понимание"]}], "analytical_thinking": [{"pattern": "data_analysis", "steps": ["Давайте проанализируем имеющуюся информацию.", "Основываясь на фактах...", "Данные показывают, что...", "Логический вывод таков..."], "keywords": ["данные", "факты", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "информация"]}, {"pattern": "pattern_recognition", "steps": ["Интересно отметить закономерность...", "Я вижу определенную схему в...", "Это напоминает мне о...", "Похожие ситуации обычно..."], "keywords": ["схема", "закономерность", "паттерн", "похожий"]}]}, "reasoning_triggers": {"complex_questions": ["Это сложный вопрос, требующий размышлений.", "Позвольте мне подумать над этим логически.", "Интересная задача для анализа.", "Давайте разберем это пошагово."], "contradictions": ["Здесь есть некоторое противоречие...", "С одной стороны... но с другой...", "Это создает интересную дилемму.", "Нужно найти баланс между..."], "uncertainty": ["Здесь много неизвестных факторов.", "Трудно дать однозначный ответ, но...", "Рассматривая различные возможности...", "В условиях неопределенности важно..."]}, "logical_frameworks": {"if_then_reasoning": {"structure": "Если {condition}, то {consequence}", "examples": ["Если мы примем это решение, то вероятными последствиями будут...", "Если рассмотреть эту гипотезу, то логично предположить...", "Если изменить подход, то результат может быть..."]}, "hypothesis_testing": {"structure": "Предположим, что {hypothesis}. Тогда мы должны увидеть {evidence}", "examples": ["Предположим, что проблема в... Тогда мы должны наблюдать...", "Если эта теория верна, то следует ожидать...", "Проверим эту идею: если она правильная, то..."]}, "analogical_reasoning": {"structure": "Это похоже на {analogy}, где {similarity}", "examples": ["Это напоминает ситуацию с..., где также...", "Аналогично тому, как..., здесь мы видим...", "По аналогии с..., можно предположить..."]}}, "decision_making_frameworks": [{"name": "SWOT анализ", "description": "Анализ сильных и слабых сторон, возможностей и угроз", "steps": ["Рассмотрим сильные стороны...", "Учтем слабые места...", "Оценим возможности...", "Не забудем об угрозах..."]}, {"name": "Матрица решений", "description": "Сравнение вариантов по критериям", "steps": ["Определим критерии оценки...", "Сравним варианты по каждому критерию...", "Взвесим важность каждого фактора...", "Выберем оптимальное решение..."]}, {"name": "Дерево решений", "description": "Пошаговый анализ последствий", "steps": ["Начнем с текущей ситуации...", "Рассмотрим возможные действия...", "Проанализируем последствия каждого...", "Выберем путь с лучшим исходом..."]}], "critical_thinking_questions": ["А что если мы рассмотрим это с противоположной точки зрения?", "Какие предположения мы делаем?", "Есть ли альтернативные объяснения?", "Какие доказательства поддерживают эту идею?", "Что может пойти не так?", "Как это связано с более широкой картиной?", "Какие долгосрочные последствия?", "Кто еще может быть затронут этим решением?"], "reasoning_connectors": ["Следовательно...", "Таким образом...", "Отсюда следует, что...", "Логично предположить...", "Исходя из этого...", "В результате...", "Поэтому...", "Соответственно..."]}